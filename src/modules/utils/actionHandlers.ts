// Action handlers for file and folder operations
export interface ActionHandlers {
  onDownload: (item: any) => void;
  onRename: (item: any) => void;
  onDuplicate: (item: any) => void;
  onShare: (item: any) => void;
  onCopyLink: (item: any) => void;
  onMove: (item: any) => void;
  onFileInformation: (item: any) => void;
  onRemove: (item: any) => void;
  onOpenWith: (item: any) => void;
}

export interface FolderActionHandlers {
  onOpenWith: (folder: any) => void;
  onRename: (folder: any) => void;
  onShare: (folder: any) => void;
  onRemove: (folder: any) => void;
  onMove: (folder: any) => void;
  onFileInformation: (folder: any) => void;
  onDownload: (folder: any) => void;
}

// File action handlers
export const createFileActionHandlers = (handlers: ActionHandlers) => {
  return {
    handleDownload: (item: any) => {
      console.log('Download action triggered for:', item);
      handlers.onDownload(item);
    },

    handleRename: (item: any) => {
      console.log('Rename action triggered for:', item);
      handlers.onRename(item);
    },

    handleDuplicate: (item: any) => {
      console.log('Duplicate action triggered for:', item);
      handlers.onDuplicate(item);
    },

    handleShare: (item: any) => {
      console.log('Share action triggered for:', item);
      handlers.onShare(item);
    },

    handleCopyLink: (item: any) => {
      console.log('Copy link action triggered for:', item);
      handlers.onCopyLink(item);
    },

    handleMove: (item: any) => {
      console.log('Move action triggered for:', item);
      handlers.onMove(item);
    },

    handleFileInformation: (item: any) => {
      console.log('File information action triggered for:', item);
      handlers.onFileInformation(item);
    },

    handleRemove: (item: any) => {
      console.log('Remove action triggered for:', item);
      handlers.onRemove(item);
    }
  };
};

// Folder action handlers
export const createFolderActionHandlers = (handlers: FolderActionHandlers) => {
  return {
    handleOpenWith: (folder: any) => {
      console.log('Open with action triggered for folder:', folder);
      handlers.onOpenWith(folder);
    },

    handleRename: (folder: any) => {
      console.log('Rename action triggered for folder:', folder);
      handlers.onRename(folder);
    },

    handleShare: (folder: any) => {
      console.log('Share action triggered for folder:', folder);
      handlers.onShare(folder);
    },

    handleRemove: (folder: any) => {
      console.log('Remove action triggered for folder:', folder);
      handlers.onRemove(folder);
    },

    handleMove: (folder: any) => {
      console.log('Move action triggered for folder:', folder);
      handlers.onMove(folder);
    },

    handleFileInformation: (folder: any) => {
      console.log('File information action triggered for folder:', folder);
      handlers.onFileInformation(folder);
    },

    handleDownload: (folder: any) => {
      console.log('Download action triggered for folder:', folder);
      handlers.onDownload(folder);
    }
  };
};

// Create action list with handlers for files
export const createFileActionList = (item: any, actionHandlers: ReturnType<typeof createFileActionHandlers>) => {
  const { superActionListFile } = require('./constant');

  return superActionListFile.map((action: any) => {
    return {
      ...action,
      onClick: () => {
        switch (action.key) {
          case 1: // Download
            actionHandlers.handleDownload(item);
            break;
          case 2: // Rename
            actionHandlers.handleRename(item);
            break;
          case 3: // Duplicate
            actionHandlers.handleDuplicate(item);
            break;
          case 4: // Share
            actionHandlers.handleShare(item);
            break;
          case 5: // Copy link
            actionHandlers.handleCopyLink(item);
            break;
          case 6: // Move
            actionHandlers.handleMove(item);
            break;
          case 7: // File information
            actionHandlers.handleFileInformation(item);
            break;
          case 9: // Remove
            actionHandlers.handleRemove(item);
            break;
          default:
            console.log('Unknown action:', action.key);
        }
      }
    };
  });
};

// Create action list with handlers for folders
export const createFolderActionList = (folder: any, actionHandlers: ReturnType<typeof createFolderActionHandlers>, withShare: boolean = false) => {
  const { superActionListFolder, superActionListFolderWithShare } = require('./constant');
  const actionList = withShare ? superActionListFolderWithShare : superActionListFolder;

  return actionList.map((action: any) => {
    return {
      ...action,
      onClick: () => {
        switch (action.key) {
          case 1: // Open with
            actionHandlers.handleOpenWith(folder);
            break;
          case 2: // Rename
            actionHandlers.handleRename(folder);
            break;
          case 3: // Share (only in withShare version) or Remove (in regular version)
            if (withShare && action.value && action.value.toString().includes('Share')) {
              actionHandlers.handleShare(folder);
            } else {
              actionHandlers.handleRemove(folder);
            }
            break;
          case 4: // Remove (in withShare version)
            actionHandlers.handleRemove(folder);
            break;
            case 5: // Move
            actionHandlers.handleMove(folder);
            break;
          case 6: // File information
            actionHandlers.handleFileInformation(folder);
            break;
          case 7: // Download
            actionHandlers.handleDownload(folder);
            break;
          default:
            console.log('Unknown folder action:', action.key);
        }
      }
    };
  });
};
