// Action handlers for file and folder operations
export interface ActionHandlers {
  onDownload: (item: any) => void;
  onRename: (item: any) => void;
  onDuplicate: (item: any) => void;
  onShare: (item: any) => void;
  onCopyLink: (item: any) => void;
  onMove: (item: any) => void;
  onFileInformation: (item: any) => void;
  onRemove: (item: any) => void;
  onOpenWith: (item: any) => void;
}

export interface FolderActionHandlers {
  onOpenWith: (folder: any) => void;
  onRename: (folder: any) => void;
  onShare: (folder: any) => void;
  onRemove: (folder: any) => void;
}

// File action handlers
export const createFileActionHandlers = (handlers: ActionHandlers) => {
  return {
    handleDownload: (item: any) => {
      console.log('Download action triggered for:', item);
      handlers.onDownload(item);
    },
    
    handleRename: (item: any) => {
      console.log('Rename action triggered for:', item);
      handlers.onRename(item);
    },
    
    handleDuplicate: (item: any) => {
      console.log('Duplicate action triggered for:', item);
      handlers.onDuplicate(item);
    },
    
    handleShare: (item: any) => {
      console.log('Share action triggered for:', item);
      handlers.onShare(item);
    },
    
    handleCopyLink: (item: any) => {
      console.log('Copy link action triggered for:', item);
      handlers.onCopyLink(item);
    },
    
    handleMove: (item: any) => {
      console.log('Move action triggered for:', item);
      handlers.onMove(item);
    },
    
    handleFileInformation: (item: any) => {
      console.log('File information action triggered for:', item);
      handlers.onFileInformation(item);
    },
    
    handleRemove: (item: any) => {
      console.log('Remove action triggered for:', item);
      handlers.onRemove(item);
    }
  };
};

// Folder action handlers
export const createFolderActionHandlers = (handlers: FolderActionHandlers) => {
  return {
    handleOpenWith: (folder: any) => {
      console.log('Open with action triggered for folder:', folder);
      handlers.onOpenWith(folder);
    },
    
    handleRename: (folder: any) => {
      console.log('Rename action triggered for folder:', folder);
      handlers.onRename(folder);
    },
    
    handleShare: (folder: any) => {
      console.log('Share action triggered for folder:', folder);
      handlers.onShare(folder);
    },
    
    handleRemove: (folder: any) => {
      console.log('Remove action triggered for folder:', folder);
      handlers.onRemove(folder);
    }
  };
};

// Create action list with handlers for files
export const createFileActionList = (item: any, actionHandlers: ReturnType<typeof createFileActionHandlers>) => {
  const { superActionListFile } = require('./constant');
  
  return superActionListFile.map((action: any) => {
    switch (action.key) {
      case 1: // Download
        return {
          ...action,
          onClick: () => actionHandlers.handleDownload(item)
        };
      case 2: // Rename
        return {
          ...action,
          onClick: () => actionHandlers.handleRename(item)
        };
      case 3: // Duplicate
        return {
          ...action,
          onClick: () => actionHandlers.handleDuplicate(item)
        };
      case 4: // Share
        return {
          ...action,
          onClick: () => actionHandlers.handleShare(item)
        };
      case 5: // Copy link
        return {
          ...action,
          onClick: () => actionHandlers.handleCopyLink(item)
        };
      case 6: // Move
        return {
          ...action,
          onClick: () => actionHandlers.handleMove(item)
        };
      case 7: // File information
        return {
          ...action,
          onClick: () => actionHandlers.handleFileInformation(item)
        };
      case 9: // Remove
        return {
          ...action,
          onClick: () => actionHandlers.handleRemove(item)
        };
      default:
        return action;
    }
  });
};

// Create action list with handlers for folders
export const createFolderActionList = (folder: any, actionHandlers: ReturnType<typeof createFolderActionHandlers>, withShare: boolean = false) => {
  const { superActionListFolder, superActionListFolderWithShare } = require('./constant');
  const actionList = withShare ? superActionListFolderWithShare : superActionListFolder;
  
  return actionList.map((action: any) => {
    switch (action.key) {
      case 1: // Open with
        return {
          ...action,
          onClick: () => actionHandlers.handleOpenWith(folder)
        };
      case 2: // Rename
        return {
          ...action,
          onClick: () => actionHandlers.handleRename(folder)
        };
      case 3: // Share (only in withShare version) or Remove (in regular version)
        if (withShare && action.value && action.value.toString().includes('Share')) {
          return {
            ...action,
            onClick: () => actionHandlers.handleShare(folder)
          };
        } else {
          return {
            ...action,
            onClick: () => actionHandlers.handleRemove(folder)
          };
        }
      case 4: // Remove (in withShare version)
        return {
          ...action,
          onClick: () => actionHandlers.handleRemove(folder)
        };
      default:
        return action;
    }
  });
};
