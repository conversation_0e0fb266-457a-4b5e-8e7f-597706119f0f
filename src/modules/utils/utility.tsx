"use client"
import { ActionIcon } from "@/assets/icons";
import { ActionDropdown } from "@dilpesh/kgk-ui-library";


export const getColoumnAssetListing = ({ onFolderClick, onFileClick, onFileInformation, fileActionHandlers, folderActionHandlers }: any) => {
    return {
        name: {
            renderChildren: (item: any) => {
                const handleClick = () => {
                    if (item.type === 'folder' && onFolderClick) {
                        onFolderClick(item.originalData);
                    } else if (item.type === 'file' && onFileClick) {
                        onFileClick(item.originalData);
                    }
                };

                return (
                    <div
                        className='icon_name_24_12_bes'
                        onClick={handleClick}
                        style={{ cursor: 'pointer' }}
                    >
                        {item.nameIcon}
                        <p>{item.name}</p>
                    </div>
                );
            },
        },
        owner: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.ownerIcon} alt="" />
                        <p>{item.owner}</p>
                    </div>
                );
            },
        },
        lastModifiedBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.lastModifiedByIcon} alt="" />
                        <p>{item.lastModifiedBy}</p>
                    </div>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                // Import the action list from constants
                const { superActionListFile } = require('../utils/constant');
                const { createFileActionHandlers, createFileActionList } = require('./actionHandlers');

                // Create action list with click handlers
                let actionitemList;

                console.log('fileActionHandlers', fileActionHandlers);

                if (item.type === 'file' && fileActionHandlers) {
                    // Use action handlers for files
                    actionitemList = createFileActionList(item.originalData, createFileActionHandlers(fileActionHandlers));
                } else {
                    // Fallback to original implementation
                    actionitemList = superActionListFile;
                }

                return (
                    <div className='action_wpr'>
                        {/* <a><ShareIcon /></a>
                        <a><LinkIcon /></a>
                        <a><DownloadIcon /></a>
                        <a><EditIcon /></a> */}
                        <ActionDropdown
                            items={{
                                dropId: 'super-action'
                            }}
                            actionList={actionitemList}
                            actionIcon={<ActionIcon />}
                        />
                    </div>
                );
            },
        },
    };
}
