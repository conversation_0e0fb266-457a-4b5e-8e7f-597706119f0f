import { GridIcon, ListIcon } from "@/assets/icons";
import { Btn, SquareBtnGroup } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { useEffect, useState } from "react";
import { AssetsSettingModalStyle } from "./assets-setting-modal.style";

export default function AssetsSettingModal({
    show,
    onHide,
    rejectBtnEvent,
    storage_data
}: any) {

    const { storage_consumed = 0, storage_limit = 1, default_view = 'grid' } = storage_data || {};



    const viewOptions = [
        { value: 'grid', content: <GridIcon /> },
        { value: 'listing', content: <ListIcon /> }
    ];
    const getFormattedSize = (bytes: number) => {
        const gb = bytes / (1024 ** 3);
        if (gb >= 1) return `${gb.toFixed(2)} GB`;
        return `${(bytes / (1024 ** 2)).toFixed(2)} MB`;
    };

    const usedPercent = Math.min((storage_consumed / storage_limit) * 100, 100).toFixed(2);
    const formattedUsed = getFormattedSize(storage_consumed);
    const formattedLimit = getFormattedSize(storage_limit);

    const [activeView, setActiveView] = useState(default_view);

    useEffect(() => {
        setActiveView(default_view);
    }, [default_view]);

    return (
        <AssetsSettingModalStyle
            title="Setting"
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal assets_setting_modal"
            footer={[
                <Btn key="cancel" onClick={rejectBtnEvent} size="large">
                    Cancel
                </Btn>,
                <Btn key="save" bg="fill" size="large">
                    Save
                </Btn>,
            ]}
        >
            <div className="upper_storedata">
                <h3>Storage</h3>
                <div className="progress_bar">
                    <span
                        className="progress_bar_fill"
                        style={{ width: `${usedPercent}%` }}
                        aria-label={`Storage used: ${usedPercent}%`}
                    />
                </div>
                <p>{`${formattedUsed} of ${formattedLimit} used`}</p>
            </div>

            <div className="mode_view">
                <h4>Default view</h4>
                <SquareBtnGroup
                    data={viewOptions}
                    value={activeView}
                    onChange={(e: any) => setActiveView(e.target.value)}
                />
            </div>
        </AssetsSettingModalStyle>
    );
}
