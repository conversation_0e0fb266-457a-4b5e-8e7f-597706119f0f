import { Btn, InputGroups } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { Col, Row } from "antd";
import { useState, useEffect } from "react";
import { AssetsRenameModalStyle } from "./assets-rename-modal.style";

interface AssetsRenameModalProps {
    show: boolean;
    onHide: () => void;
    rejectBtnEvent: () => void;
    item?: any;
    itemType: 'file' | 'folder';
    onSuccess?: (newName: string) => void;
}

export default function AssetsRenameModal({
    show,
    onHide,
    rejectBtnEvent,
    item,
    itemType,
    onSuccess,
}: AssetsRenameModalProps) {
    const [newName, setNewName] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Set initial name when modal opens
    useEffect(() => {
        if (show && item) {
            const currentName = itemType === 'folder' ? item.folder_name : item.file_name;
            setNewName(currentName || '');
            setError('');
        }
    }, [show, item, itemType]);

    const handleCancel = () => {
        setNewName('');
        setError('');
        rejectBtnEvent();
    };

    const handleRename = async () => {
        if (!newName.trim()) {
            setError(`${itemType === 'folder' ? 'Folder' : 'File'} name is required`);
            return;
        }

        if (newName.trim() === (itemType === 'folder' ? item?.folder_name : item?.file_name)) {
            setError('Please enter a different name');
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            // TODO: Replace with actual API call
            console.log(`Renaming ${itemType}:`, {
                id: item?._id,
                currentName: itemType === 'folder' ? item?.folder_name : item?.file_name,
                newName: newName.trim(),
                itemType
            });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Call success callback
            onSuccess?.(newName.trim());
            
            // Close modal
            onHide();
            
            // Reset form
            setNewName('');
            setError('');
            
        } catch (error: any) {
            console.error(`Error renaming ${itemType}:`, error);
            setError(error.message || `Failed to rename ${itemType}`);
        } finally {
            setIsLoading(false);
        }
    };

    const getTitle = () => {
        return `Rename ${itemType === 'folder' ? 'folder' : 'file'}`;
    };

    const getLabel = () => {
        return `${itemType === 'folder' ? 'Folder' : 'File'} name`;
    };

    const getPlaceholder = () => {
        return `Enter ${itemType === 'folder' ? 'folder' : 'file'} name`;
    };

    return (
        <AssetsRenameModalStyle
            title={getTitle()}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal"
            footer={[
                <Btn key="cancel" onClick={handleCancel} size="large" disabled={isLoading}>
                    Cancel
                </Btn>,
                <Btn
                    key="rename"
                    bg="fill"
                    size="large"
                    onClick={handleRename}
                    isloading={isLoading}
                    disabled={!newName.trim() || isLoading}
                >
                    Rename
                </Btn>,
            ]}
        >
            <form className="body" onSubmit={(e) => { e.preventDefault(); handleRename(); }}>
                <Row>
                    <Col xs={24}>
                        <InputGroups
                            label={getLabel()}
                            value={newName}
                            onChange={(e: any) => setNewName(e.target.value)}
                            placeholder={getPlaceholder()}
                            disabled={isLoading}
                            error={error}
                        />
                    </Col>
                </Row>
            </form>
        </AssetsRenameModalStyle>
    )
}
