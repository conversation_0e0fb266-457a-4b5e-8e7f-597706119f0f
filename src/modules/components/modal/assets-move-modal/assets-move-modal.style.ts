import { Media, bes, bl } from "@dilpesh/kgk-ui-library";
import { Modal } from "antd";
import styled, { css } from "styled-components";

export const AssetsMoveModalStyle = styled(Modal)`
    ${(props) =>
        css`
            margin-top: 32px;
            margin-bottom: 32px;
            .ant-modal-content {
                padding: 0;
                .ant-modal-header {
                    padding: 32px;
                    margin-bottom: 0;
                    @media ${Media.ultra_mobile}{
                        padding: 24px;
                    }
                    .custom_header {
                        & > h2 {
                            ${bl(props?.theme?.text?.high, 500)};
                        }
                        .current_location {
                            margin-top: 24px;
                            display: flex;
                            align-items: center;
                            @media ${Media.ultra_mobile}{
                                margin-top: 16px;
                            }
                            & > p {
                                ${bes(props?.theme?.text?.high, 400)};
                                margin-right: 16px;
                                @media ${Media.ultra_mobile}{
                                    margin-right: 12px;
                                }
                            }
                            .folder_icon {
                                display: flex;
                                align-items: center;
                                flex-grow: 1;
                                svg {
                                    width: 24px;
                                    height: 24px;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                                & > p {
                                    width: calc(100% - 24px);
                                    ${bes(props?.theme?.text?.high, 500)};
                                    padding-left: 16px;
                                    @media ${Media.ultra_mobile}{
                                        padding-left: 8px;
                                    }
                                }
                            }
                        }
                    }
                }
                .ant-modal-body {
                    .custom_modal_body {
                        .item_wrap {
                            padding: 16px 32px;
                            display: flex;
                            align-items: center;
                            transition: all 300ms ease-in;
                            @media ${Media.ultra_mobile}{
                                padding: 12px 24px;
                            }
                            .dynamic_part {
                                display: flex;
                                align-items: center;
                                flex-grow: 1;
                                & > svg {
                                    width: 24px;
                                    height: 24px;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                                .item_name {
                                    padding-left: 16px;
                                    ${bes(props?.theme?.text?.high, 500)};
                                    width: calc(100% - 24px);
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                    overflow: hidden;
                                    @media ${Media.ultra_mobile}{
                                        padding-left: 8px;
                                    }
                                }
                            }
                            .move_section {
                                opacity: 0;
                                margin-left: auto;
                                padding-left: 12px;
                                display: flex;
                                align-items: center;
                                transition: opacity 300ms ease-in;
                                & > p {
                                    ${bes(props?.theme?.colors?.brand_high, 500)};
                                    width: calc(100% - 24px);
                                    padding-right: 12px;
                                    @media ${Media.ultra_mobile}{
                                        padding-right: 8px;
                                    }
                                }
                                & > svg {
                                    width: 24px;
                                    height: 24px;
                                    path{
                                        stroke: ${(props.theme.colors.brand_high)};
                                    }
                                }
                            }
                            &:hover {
                                background-color: ${(props) => props?.theme?.colors?.brand_low};
                                transition: all 300ms ease-in;
                                .move_section {
                                    opacity: 1;
                                    transition: opacity 300ms ease-in;
                                }
                            }
                        }
                    }
                }
                .ant-modal-footer {
                    margin-top: 0px;
                    .custom_footer {
                        display: flex;
                        padding: 32px;
                        align-items: center;
                        @media ${Media.ultra_mobile}{
                            padding: 24px;
                        }
                        .new_folderwpr{
                            svg {
                                width: 32px;
                                height: 32px;
                                path{
                                    stroke: ${(props.theme.colors.brand_high)};
                                }
                            }
                        }
                        .buttons_wrap {
                            margin-left: auto;
                            button {
                                margin-right: 16px;
                                min-width: 104px;
                                &:last-child {
                                    margin-right: 0;
                                }
                            }
                        }
                    }
                }
            }
        `
    }
`