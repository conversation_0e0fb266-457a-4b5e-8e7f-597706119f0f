import { FolderAssetsIcon, FolderList, FolderUploadIcon, MoveIcon } from "@/assets/icons";
import { Btn } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import Link from "next/link";
import { AssetsMoveModalStyle } from "./assets-move-modal.style";

export default function AssetsMoveModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {
    return (
        <AssetsMoveModalStyle
            title={
                <div className="custom_header">
                    <h2>Move 1 item to</h2>
                    <div className="current_location">
                        <p>Current location:</p>
                        <div className="folder_icon">
                            <FolderAssetsIcon />
                            <p>Assets</p>
                        </div>
                    </div>
                </div>
            }
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="assets_move_modal"
            footer={
                <div className="custom_footer">
                    <div className="new_folderwpr">
                        <Link href="javascript:void(0)"><FolderUploadIcon /></Link>
                    </div>
                    <div className="buttons_wrap">
                        <Btn onClick={rejectBtnEvent} size="large">
                            Cancel
                        </Btn>
                        <Btn bg="fill" size="large">
                            Move
                        </Btn>
                    </div>
                </div>
            }
        >
            <div className="custom_modal_body">
                <div className="item_wrap">
                    <div className="dynamic_part">
                        <FolderAssetsIcon />
                        <p className="item_name">Banners</p>
                    </div>
                    <Link href="javascript:void(0)" className="move_section">
                        <p>Move</p>
                        <MoveIcon />
                    </Link>
                </div>
                <div className="item_wrap">
                    <div className="dynamic_part">
                        <FolderList />
                        <p className="item_name">Photos</p>
                    </div>
                    <Link href="javascript:void(0)" className="move_section">
                        <p>Move</p>
                        <MoveIcon />
                    </Link>
                </div>
                <div className="item_wrap">
                    <div className="dynamic_part">
                        <FolderAssetsIcon />
                        <p className="item_name">Collections</p>
                    </div>
                    <Link href="javascript:void(0)" className="move_section">
                        <p>Move</p>
                        <MoveIcon />
                    </Link>
                </div>
                <div className="item_wrap">
                    <div className="dynamic_part">
                        <FolderAssetsIcon />
                        <p className="item_name">Posts</p>
                    </div>
                    <Link href="javascript:void(0)" className="move_section">
                        <p>Move</p>
                        <MoveIcon />
                    </Link>
                </div>
                <div className="item_wrap">
                    <div className="dynamic_part">
                        <FolderAssetsIcon />
                        <p className="item_name">Ads</p>
                    </div>
                    <Link href="javascript:void(0)" className="move_section">
                        <p>Move</p>
                        <MoveIcon />
                    </Link>
                </div>
            </div>
        </AssetsMoveModalStyle >
    )
}