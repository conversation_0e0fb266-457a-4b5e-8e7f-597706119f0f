"use client"
import AssetsInfoModal from "@/modules/components/modal/assets-info-modal/assets-info-modal";
import AssetsMoveModal from "@/modules/components/modal/assets-move-modal/assets-move-modal";
import AssetsNewFolderModal from "@/modules/components/modal/assets-new-folder-modal/assets-new-folder-modal";
import AssetsSettingModal from "@/modules/components/modal/assets-setting-modal/assets-setting-modal";
import AssetsShareModal from "@/modules/components/modal/assets-share-modal/assets-share-modal";
import { BreadCrumbs, SortBy, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useCreateFile, useGetFolderDataQuery } from "@magneto-it-solutions/kgk-common-library";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import FilterOptions from "../../modules/components/filter-options";
import FoldersFiles from "../../modules/components/folders-files";
import NewFolderHeader from "../../modules/components/new-folder-header";
import Selectedheader from "../../modules/components/selected-header";
import UploadProgress from "../../modules/components/upoload-progress";
import { ICreateFile } from "../../modules/hooks/useFileUpload";
import { AssetsPageStyle } from "../../modules/style";
import { listViewOptions, sortOptions } from "../../modules/utils/constant";

interface BreadcrumbItem {
    link?: string;
    title: string;
    folderId?: string;
    onClick?: () => void;
}

export default function AssetsPage() {

    const ref: any = useRef();
    const router = useRouter();
    const searchParams = useSearchParams();
    const [leftPosition, setleftPosition] = useState('');
    const [sortOpen, setSortOpen] = useState(false)
    const [activeBtn, setActivebtn] = useState(listViewOptions[0].value);
    const [selectedItems, setSelectedItems] = useState<string[]>([]);

    const [anfModalOpen, setanfModalOpen] = useState(false);
    const [aiModalOpen, setaiModalOpen] = useState(false);
    const [asModalOpen, setasModalOpen] = useState(false);
    const [amModalOpen, setamModalOpen] = useState(false);
    const [asettingModalOpen, setasettingModalOpen] = useState(false);

    const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
        {
            link: 'Home',
            title: 'Home'
        },
        {
            title: 'Assets'
        }
    ]);

    // Get folder ID from URL parameters
    const currentFolderId = searchParams.get('folderId') || '';
    const { data: folderData, isLoading, isError } = useGetFolderDataQuery(currentFolderId);
    console.log('folderData', folderData);

    // File upload API hook
    const { createFile, data: uploadData, isLoading: isUploading } = useCreateFile();

    // Upload progress state
    const [uploadingFiles, setUploadingFiles] = useState<any[]>([]);
    const [showUploadProgress, setShowUploadProgress] = useState(false);

    // File information state
    const [selectedFileForInfo, setSelectedFileForInfo] = useState<any>(null);

    useLayoutEffect(() => {
        setleftPosition(ref.current.offsetLeft + 'px');
    }, []);

    // Build breadcrumbs from API response
    useEffect(() => {
        if (!currentFolderId) {
            // Root level - show default breadcrumbs
            setBreadcrumbs([
                {
                    link: '/',
                    title: 'Home'
                },
                {
                    title: 'Assets',
                }
            ]);
        } else if (folderData?.data?.parents) {
            const baseBreadcrumbs = [
                {
                    title: 'Home',
                    link: '/',
                },
                {
                    title: 'Assets',
                    onClick: () => router.push('/')
                }
            ];

            const reversedParents = folderData?.data?.parents.slice().reverse(); // Oldest parent first
            console.log('reversedParents', reversedParents);

            const parentBreadcrumbs = reversedParents.map((parent: any, index: number) => ({
                title: parent.name,
                onClick: index === reversedParents.length - 1 ? undefined : () => router.push(`?folderId=${parent.id}`)
            }));

            setBreadcrumbs([...baseBreadcrumbs, ...parentBreadcrumbs]);
        }

    }, [currentFolderId, folderData, router]);

    // Handle folder navigation
    const handleFolderNavigate = (folder: any) => {
        console.log('folder', folder);
        const params = new URLSearchParams(searchParams.toString());
        params.set('folderId', folder._id);
        router.push(`?${params.toString()}`);
        // Breadcrumbs will be updated automatically from the API response in useEffect
    };

    // Handle file upload with API
    const handleFileUpload = async (files: File[], parentFolderId?: string) => {
        console.log('Files to upload:', files);
        console.log('Parent folder ID:', parentFolderId);

        // Show upload progress
        setShowUploadProgress(true);

        // Add files to upload progress list
        const uploadFiles = files.map((file, index) => ({
            id: `${file.name}-${Date.now()}-${index}`,
            file,
            progress: 0,
            status: 'uploading'
        }));
        setUploadingFiles(uploadFiles);

        const payload: ICreateFile = {
            parent_folder_id: parentFolderId || currentFolderId || '',
            files: files
        };

        try {
            console.log('Starting upload with payload:', payload);
            const result = await createFile(payload);
            console.log('Upload completed:', result);

            // Mark all files as completed
            setUploadingFiles(prev =>
                prev.map(file => ({ ...file, progress: 100, status: 'completed' }))
            );

            // Hide upload progress after a delay
            setTimeout(() => {
                setShowUploadProgress(false);
                setUploadingFiles([]);
            }, 2000);

            // You might want to refresh the folder data here
            // refetch(); // if you have a refetch function from the query

        } catch (error) {
            console.error('Error in upload process:', error);

            // Mark all files as error
            setUploadingFiles(prev =>
                prev.map(file => ({ ...file, status: 'error' }))
            );
        }
    };

    // Upload progress helper functions
    const removeUploadingFile = (fileId: string) => {
        setUploadingFiles(prev => prev.filter(file => file.id !== fileId));
    };

    const clearCompletedUploads = () => {
        setUploadingFiles([]);
        setShowUploadProgress(false);
    };

    // Handle file information click
    const handleFileInformation = (file: any) => {
        console.log('File information requested for:', file);
        setSelectedFileForInfo(file);
        setaiModalOpen(true);
    };

    return (
        <>
            <AssetsPageStyle className="assets_landing_page first_view_content">
                <div className="container">
                    <div className="findLeft" ref={ref}></div>
                    <div className="breadcrumb_wrap">
                        <BreadCrumbs value={breadcrumbs} />
                    </div>
                    <div className="title_wrap">
                        <h1>Assets</h1>
                    </div>
                    <TabletFilterBtn
                        sort={true}
                        sortClick={() => { setSortOpen(true) }}
                    />
                    <div className="asset_content_wrap">
                        <div className="subheader_wrap">
                            {selectedItems.length > 0 ?
                                <Selectedheader />
                                :
                                <NewFolderHeader
                                    onNewFolderClick={() => setanfModalOpen(true)}
                                    onFileUpload={handleFileUpload}
                                    currentFolderId={currentFolderId}
                                />
                            }
                            <FilterOptions
                                setActivebtn={setActivebtn}
                                activeBtn={activeBtn}
                                setasettingModalOpen={setasettingModalOpen}
                            />
                        </div>
                        <SortBy
                            options={sortOptions}
                            defaultValue={sortOptions[0].value}
                            open={sortOpen}
                            close={() => { setSortOpen(false) }}
                        />
                        <FoldersFiles
                            viewMode={activeBtn}
                            folderData={folderData?.data}
                            selectedItems={selectedItems}
                            onFolderNavigate={handleFolderNavigate}
                            onFileInformation={handleFileInformation}
                            onItemSelect={(itemId: string) => {
                                setSelectedItems(prev =>
                                    prev.includes(itemId)
                                        ? prev.filter(id => id !== itemId)
                                        : [...prev, itemId]
                                );
                            }}
                        />
                    </div>

                    {/* Upload Progress Component */}
                    {(showUploadProgress || uploadingFiles.length > 0) && (
                        <UploadProgress
                            leftPosition={leftPosition}
                            uploadingFiles={uploadingFiles}
                            isUploading={isUploading}
                            onCancelUpload={removeUploadingFile}
                            onClearCompleted={clearCompletedUploads}
                        />
                    )}
                </div>
            </AssetsPageStyle >
            {anfModalOpen && (
                <AssetsNewFolderModal
                    onHide={() => setanfModalOpen(false)}
                    show={anfModalOpen}
                    currentFolderId={currentFolderId}
                    onSuccess={() => {
                        setanfModalOpen(false);
                    }}
                    rejectBtnEvent={() => setanfModalOpen(false)}
                />
            )}

            {aiModalOpen && (
                <AssetsInfoModal
                    onHide={() => setaiModalOpen(false)}
                    show={aiModalOpen}
                    rejectBtnEvent={() => setaiModalOpen(false)}
                    selectedFile={selectedFileForInfo}
                    storagePath={folderData?.data?.parents}
                />
            )}

            {asModalOpen && (
                <AssetsShareModal
                    onHide={() => setasModalOpen(false)}
                    show={asModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setasModalOpen(false)}
                />
            )}

            {asettingModalOpen && (
                <AssetsSettingModal
                    onHide={() => setasettingModalOpen(false)}
                    show={asettingModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setasettingModalOpen(false)}
                    storage_data={folderData?.data?.folderConfiguration}
                />
            )}

            {amModalOpen && (
                <AssetsMoveModal
                    onHide={() => setamModalOpen(false)}
                    show={amModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setamModalOpen(false)}
                />
            )}


        </>
    )
}